import React, { useMemo, useState } from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { Message } from "../../types/message";
import {
  MessageInfoData,
  MessageInfoHeader,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import { RelatedUsersList } from "../../types/userMails";
import { getEmailsString } from "../../helpers/getEmailsString";

// Components
import TabBarNavigator from "./tabs/components/TabBarNavigator";
import MessageBottomButtons from "../general/MessageBottomButtons";

type Props = {
  width: number;
  message: Message;
  replies: any;
  comments: any;
  messageAttachmentsIds: any;
  attachments: any;
  attachmentsCount: number;
  downloadedAttachments: {};
  foldersIds: string[];
  messageFolders: any;
  caseMetadata: any;
  cases: any;
  recipientsEmails: RelatedUsersList;
  notifiedUsers?: RelatedUsersList;
  getMessageBodyLoading: boolean;
  getMessageCommentsLoading: boolean;
  getMessageActionsLoading: boolean;
  messageBodyError: string;
  getMessageCommentsError: string;
  getMessageActionsError: string;
  copyOrMoveMessageFolderError: string;
  moveToFolder: () => void;
  copyToFolder: () => void;
  getMessageActions: () => void;
  handleRetryMessageBody: () => void;
  setDownloadedAttachments: (payload) => void;
  clearDownloadedAttachments: () => void;
  handleClearCopyOrMoveMessageFolderError: () => void;
  postNewComment: () => void;
  onPressNewComment: () => void;
  onPressCommentReply: (commentId: string) => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleStarComment: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  attachmentsLength: number;
  handleDownloadAttachment: (id: string) => void;
  handleDownloadAllAttachments: (id: string[]) => void;
  isAnyFileDownloadLoading: boolean;
  isDraft: boolean;
  handleEditDraftMessage: () => void;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressReply: () => void;
  onPressReplyAll: () => void;
  onPressForward: () => void;
};

const MessageScreen = ({
  width,
  message,
  replies,
  comments,
  messageAttachmentsIds,
  attachments,
  attachmentsCount,
  downloadedAttachments,
  foldersIds,
  messageFolders,
  caseMetadata,
  cases,
  recipientsEmails,
  notifiedUsers,
  getMessageBodyLoading,
  getMessageCommentsLoading,
  getMessageActionsLoading,
  messageBodyError,
  getMessageCommentsError,
  getMessageActionsError,
  copyOrMoveMessageFolderError,
  moveToFolder,
  copyToFolder,
  getMessageActions,
  handleRetryMessageBody,
  setDownloadedAttachments,
  clearDownloadedAttachments,
  handleClearCopyOrMoveMessageFolderError,
  postNewComment,
  onPressNewComment,
  onPressCommentReply,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleStarComment,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  isAnyFileDownloadLoading,
  isDraft,
  handleEditDraftMessage,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  onPressReply,
  onPressReplyAll,
  onPressForward,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [activeTab, setActiveTab] = useState(0);

  // Memoize buttons to prevent unnecessary re-renders
  const BUTTONS = {
    0: isDraft
      ? [
          {
            title: "Edit",
            isDisabled: false,
            onPress: handleEditDraftMessage,
          },
        ]
      : [
          {
            title: "Reply",
            isDisabled: false,
            onPress: onPressReply,
          },
          {
            title: "Reply All",
            isDisabled: false,
            onPress: onPressReplyAll,
          },
          {
            title: "Forward",
            isDisabled: false,
            onPress: onPressForward,
          },
        ],
    1: [
      {
        title: "Add new comment",
        isDisabled: false,
        onPress: onPressNewComment,
      },
    ],
    2: [
      {
        title: `Download All (${attachmentsCount || 0})`,
        isDisabled:
          !attachmentsCount ||
          attachmentsCount <= 0 ||
          isAnyFileDownloadLoading,
        onPress: () => handleDownloadAllAttachments(messageAttachmentsIds),
      },
    ],
    3: [
      {
        title: "Move to Folder",
        isDisabled: false,
        onPress: moveToFolder,
      },
      {
        title: "Copy to Folder",
        isDisabled: false,
        onPress: copyToFolder,
      },
    ],
    4: [], // Cases tab - read only
  };

  return (
    <View style={{ flex: 1 }}>
      <ScrollView style={styles.container}>
        <MessageInfoHeader
          subject={message?.subject}
          sentDate={message?.sentDate}
          containerStyle={styles.messageInfoContainer}
        />

        <MessageInfoData
          avatarName={message?.avatarName}
          inOut={message?.inOut}
          username={message?.username}
          from={message?.from}
          tos={getEmailsString(message?.tos)}
          ccs={getEmailsString(message?.ccs)}
          bccs={getEmailsString(message?.bccs)}
          containerStyle={styles.messageInfoContainer}
        />

        <TabBarNavigator
          width={width}
          replies={replies}
          comments={comments}
          messageAttachmentsIds={messageAttachmentsIds}
          attachments={attachments}
          attachmentsCount={attachmentsCount}
          downloadedAttachments={downloadedAttachments}
          foldersIds={foldersIds}
          messageFolders={messageFolders}
          caseMetadata={caseMetadata}
          cases={cases}
          recipientsEmails={recipientsEmails}
          html={message?.fullMessageBody || ""}
          getMessageBodyLoading={getMessageBodyLoading}
          getMessageCommentsLoading={getMessageCommentsLoading}
          getMessageActionsLoading={getMessageActionsLoading}
          getMessageCommentsError={getMessageCommentsError}
          getMessageActionsError={getMessageActionsError}
          messageBodyError={messageBodyError}
          copyOrMoveMessageFolderError={copyOrMoveMessageFolderError}
          moveToFolder={moveToFolder}
          copyToFolder={copyToFolder}
          getMessageActions={getMessageActions}
          setDownloadedAttachments={setDownloadedAttachments}
          clearDownloadedAttachments={clearDownloadedAttachments}
          handleClearCopyOrMoveMessageFolderError={
            handleClearCopyOrMoveMessageFolderError
          }
          postNewComment={postNewComment}
          postMessageCommentLoading={postMessageCommentLoading}
          postMessageCommentError={postMessageCommentError}
          postMessageCommentSuccess={postMessageCommentSuccess}
          handlePostMessageCommentSuccessDismiss={
            handlePostMessageCommentSuccessDismiss
          }
          handleStarComment={handleStarComment}
          handleAddAttachment={handleAddAttachment}
          uploadAttachmentLoading={uploadAttachmentLoading}
          uploadAttachmentError={uploadAttachmentError}
          attachmentsLength={attachmentsLength}
          handleDownloadAttachment={handleDownloadAttachment}
          handleDownloadAllAttachments={handleDownloadAllAttachments}
          isAnyFileDownloadLoading={isAnyFileDownloadLoading}
          isDraft={isDraft}
          handleEditDraftMessage={handleEditDraftMessage}
          onPressReply={onPressReply}
          onPressReplyAll={onPressReplyAll}
          onPressForward={onPressForward}
          onPressNewComment={onPressNewComment}
          onPressCommentReply={onPressCommentReply}
          notifiedUsers={notifiedUsers}
          fetchNotifiedUsers={fetchNotifiedUsers}
          fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
          fetchNotifiedUsersError={fetchNotifiedUsersError}
          handleRetryMessageBody={handleRetryMessageBody}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />
      </ScrollView>

      {!getMessageBodyLoading && activeTab !== 4 && (
        <MessageBottomButtons buttons={BUTTONS[activeTab]} />
      )}
    </View>
  );
};

export default React.memo(MessageScreen);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
    },
    messageInfoContainer: {
      padding: SPACING.M,
    },
  });

  return { styles, color };
};
