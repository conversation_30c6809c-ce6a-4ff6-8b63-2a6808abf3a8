import React from "react";
import { StyleSheet, View } from "react-native";
import RenderHtml from "react-native-render-html";

// Components
import { FONT_SIZES, ICON_POSITIONS, IconTextButton, SPACING } from "b-ui-lib";
import TabErrorMessage from "../../TabErrorMessage";

type Props = {
  html: string;
  width: number;
  messageBodyError: string;
  handleRetryMessageBody: () => void;
};

const HtmlMessageContent = ({
  html,
  width,
  messageBodyError,
  handleRetryMessageBody,
}: Props) => {
  const styles = createStyles();

  // Calculate effective content width accounting for HtmlTab's horizontal padding (SPACING.M * 2 = 32px)
  const effectiveContentWidth = width - SPACING.M * 2;

  if (messageBodyError) {
    return (
      <View style={styles.errorContainer}>
        <TabErrorMessage
          text={messageBodyError}
          isVisible={Boolean(messageBodyError)}
        />

        <IconTextButton
          iconPosition={ICON_POSITIONS.left}
          iconName="Redo"
          iconSize={20}
          iconColor="#007AFF"
          title="Retry"
          onPress={handleRetryMessageBody}
          textStyle={styles.retryButtonText}
          containerStyle={styles.retryButton}
        />
      </View>
    );
  }

  return (
    <RenderHtml
      contentWidth={effectiveContentWidth}
      source={{ html }}
      enableExperimentalMarginCollapsing={true}
      computeEmbeddedMaxWidth={(contentWidth) => contentWidth}
      ignoredDomTags={["map", "area"]}
      // renderersProps={{
      //   img: {
      //     enableExperimentalPercentWidth: true,
      //   },
      //   blockquote: {
      //     htmlAttribs: {
      //       style:
      //         "margin: 16px 0; padding: 16px; border-left: 4px solid #ddd; background: #f9f9f9;",
      //     },
      //   },
      // }}
      // defaultTextProps={{
      //   style: styles.htmlTextStyle,
      // }}
      tagsStyles={{
        body: {
          fontSize: FONT_SIZES.FOURTEEN,
          lineHeight: 20,
        },
        p: {
          marginBottom: 12,
        },
        div: {
          marginBottom: 8,
        },
        // Add mobile-friendly table styles
        // table: {
        //   width: '100%',
        // },
        // td: {
        //   padding: 8,
        //   fontSize: FONT_SIZES.FOURTEEN,
        //   lineHeight: 18,
        // },
        // th: {
        //   padding: 8,
        //   fontSize: FONT_SIZES.FOURTEEN,
        //   fontWeight: '600',
        //   lineHeight: 18,
        // },
      }}
      systemFonts={["System"]}
    />
  );
};

// HtmlMessageContent.displayName = 'HtmlMessageContent';

const createStyles = () => {
  const styles = StyleSheet.create({
    scrollContainer: {
      flex: 1,
    },
    htmlTextStyle: {
      color: "#000000",
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: 20,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: SPACING.L,
    },
    retryButton: {
      marginTop: SPACING.M,
      paddingHorizontal: SPACING.L,
      paddingVertical: SPACING.S,
      backgroundColor: "#FFFFFF",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: "#007AFF",
    },
    retryButtonText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "600",
      color: "#007AFF",
    },
    // Android-specific styles
    androidScrollContent: {
      flexGrow: 1,
    },
    androidHorizontalScroll: {
      flex: 1,
    },
    androidOuterScroll: {
      flex: 1,
      height: "100%",
    },
    androidHorizontalContent: {
      minWidth: "100%",
      flexGrow: 1,
    },
    androidInnerScroll: {
      width: 800, // Fixed width for the inner scroll
      minHeight: "100%",
    },
    androidWideContentContainer: {
      flex: 1,
      minHeight: 400, // Ensure minimum height
    },
    androidHorizontalScrollOnly: {
      flexGrow: 0,
      height: "auto",
    },
  });

  return styles;
};

export default React.memo(HtmlMessageContent);
