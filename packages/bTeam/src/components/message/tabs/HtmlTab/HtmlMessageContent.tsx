import React from "react";
import { StyleSheet, View } from "react-native";
import RenderHtml from "react-native-render-html";

// Components
import { FONT_SIZES, ICON_POSITIONS, IconTextButton, SPACING } from "b-ui-lib";
import TabErrorMessage from "../../TabErrorMessage";

type Props = {
  html: string;
  width: number;
  messageBodyError: string;
  handleRetryMessageBody: () => void;
};

const HtmlMessageContent = ({
  html,
  width,
  messageBodyError,
  handleRetryMessageBody,
}: Props) => {
  const styles = createStyles();

  // Calculate effective content width accounting for HtmlTab's horizontal padding (SPACING.M * 2 = 32px)
  const effectiveContentWidth = width - SPACING.M * 2;

  console.log("html", html);

  if (messageBodyError) {
    return (
      <View style={styles.errorContainer}>
        <TabErrorMessage
          text={messageBodyError}
          isVisible={Boolean(messageBodyError)}
        />

        <IconTextButton
          iconPosition={ICON_POSITIONS.left}
          iconName="Redo"
          iconSize={20}
          iconColor="#007AFF"
          title="Retry"
          onPress={handleRetryMessageBody}
          textStyle={styles.retryButtonText}
          containerStyle={styles.retryButton}
        />
      </View>
    );
  }

  return (
    <RenderHtml
      contentWidth={effectiveContentWidth}
      source={{
        html: `
<table
  style="
    font-size: 12px;
    margin-top: 5px;
    font-family: Verdana;
    margin-bottom: 10px;
    border-spacing: 0px;
  "
>
  <tr>
    <td>Message Id: [BNFT-1953157]</td>
  </tr>
</table>
<p
  style="
    font-family: Verdana, Arial, sans-serif;
    font-size: 12px;
    margin-bottom: 16px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding: 0;
  "
>
  test
</p>

<table
  cellpading="0"
  cellspacing="0"
  style="font-family: Arial; margin-bottom: -4px; margin: 0; padding: 0"
>
  <tbody>
    <tr>
      <td colspan="2">
        <strong style="color: #fbb034; font-size: 12pt; font-weight: bold"
          >Konstantinos X. Thelouras</strong
        >
        <span style="color: #58595b; font-size: 12pt"> | Product Owner</span>
      </td>
    </tr>
  </tbody>
</table>

<table
  cellpading="0"
  cellspacing="0"
  style="
    border-spacing: 0 11.338582677px;
    font-family: Arial;
    margin: 0;
    padding: 0;
    width: 415.748031496px;
  "
>
  <tbody>
    <tr style="background-color: black">
      <td align="left" style="color: white; margin: 0px; padding: 0px">
        <img
          alt="Benefit Software"
          height="100"
          src="https://www.benefit.gr/img/sig/BENEFIT-CII-SIGNATURE-LOGO-NEGATIVE3.png"
          title="Benefit Software"
          usemap="#bnftlogomap"
          width="170"
        /><map name="bnftlogomap"
          ><area
            alt="Benefit Software"
            coords="5,10,135,57"
            href="https://www.benefit.gr"
            shape="rect"
            target="_blank"
        /></map>
      </td>
      <td align="center" style="margin: 0px; padding: 0px">
        <table>
          <tbody>
            <tr>
              <td style="color: white; margin: 0 0 0 0; padding: 0">
                <span style="float: left; font-size: 8pt; margin: 0; padding: 0"
                  >5 Aitolikou Street, 185 45, Piraeus, Greece</span
                >
              </td>
            </tr>
            <tr>
              <td
                style="color: white; float: left; margin: 4px 0 0 0; padding: 0"
              >
                <span style="float: left; font-size: 8pt; margin: 0; padding: 0"
                  ><strong>T.</strong> +30 210 42 93 000, +30 210 42 87
                  297</span
                >
              </td>
            </tr>
            <tr>
              <td
                style="color: white; float: left; margin: 0px 0 0 0; padding: 0"
              >
                <span style="float: left; font-size: 8pt; margin: 0; padding: 0"
                  ><span style="float: left; margin-left: 11px"
                    >+30 210 42 87 298</span
                  ></span
                >
              </td>
            </tr>
            <tr>
              <td
                style="color: white; float: left; margin: 4px 0 0 0; padding: 0"
              >
                <span style="float: left; font-size: 8pt; margin: 0; padding: 0"
                  ><strong>E.</strong>
                  <a
                    href="mailto:<EMAIL>"
                    style="color: white; text-decoration: none"
                  >
                    <EMAIL></a
                  >
                </span>
              </td>
            </tr>
            <tr>
              <td
                style="color: white; float: left; margin: 4px 0 0 0; padding: 0"
              >
                <span style="float: left; font-size: 8pt; margin: 0; padding: 0"
                  ><strong>W.</strong>
                  <a
                    href="https://www.benefit.gr/"
                    style="color: white; text-decoration: none"
                    target="_blank"
                  >
                    benefit.gr
                  </a>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="2" style="margin: 0px; padding: 0px">
        <span style="float: left; height: 19px; overflow: hidden"
          ><img
            src="https://www.benefit.gr/img/sig/benefit-follow-us.png"
            usemap="#followusmap"
          /><map name="followusmap"
            ><area
              alt="f"
              coords="110,3,123,16"
              href="https://www.facebook.com/BenefitSoftware/"
              shape="rect"
              target="_blank" />
            <area
              alt="in"
              coords="131,3,145,16"
              href="https://www.linkedin.com/company/benefit-software/"
              shape="rect"
              target="_blank"
          /></map>
        </span>
      </td>
    </tr>
    <tr>
      <td colspan="2" style="margin: 0px">
        <a href="https://www.benefit.gr/product/cii-reporting-system/"
          ><img
            alt="CII-Reporting-System"
            height="96"
            src="https://www.benefit.gr/img/sig/benefit_mail.signature.june24 copy.png"
            width="410"
        /></a>
      </td>
    </tr>
    <tr>
      <td
        colspan="2"
        style="color: green; font-size: 8pt; margin: 0px; padding: 0px"
      >
        Think before you print
      </td>
    </tr>
  </tbody>
</table>

<div style="text-align: justify">
  <p
    style="
      font-family: Verdana, Arial, sans-serif;
      font-size: 12px;
      margin-bottom: 16px;
      margin-left: 0;
      margin-right: 0;
      margin-top: 0;
      padding: 0;
    "
  >
    <span style="color: #7f8c8d"
      ><strong><em>MESSAGE CONFIDENTIALITY &amp; SECURITY NOTICE:</em></strong
      ><em
        >&nbsp;This message and/or its attachments may contain confidential and
        privileged information and is intended for the named person or entity to
        which it is addressed. Any use, copying or distribution of this
        information by anyone other than the intended recipient(s) is prohibited
        by law. If you receive this in error, please immediately delete it from
        your system and notify the sender. The contents of this message contain
        personal opinions of the sender, which are not the official views of
        Benefit Software&nbsp;nor do they consist a provision of financial or
        advisory services unless expressly stated otherwise. This message is not
        a solicitation and/or an offer or acceptance of any proposal in relation
        to any contract or transaction unless expressly otherwise indicated in
        the message itself. The Internet is not a secure or error-free
        environment, and Benefit Software does not accept liability for any loss
        or damage arising from the use of this message or from delayed,
        intercepted, corrupted or virus-infected e-mail transmission.</em
      ></span
    >
  </p>
</div>

<p
  style="
    font-family: Verdana, Arial, sans-serif;
    font-size: 12px;
    margin-bottom: 16px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding: 0;
  "
>
  <br />
</p>

<hr />
<p
  style="
    font-family: Verdana, Arial, sans-serif;
    font-size: 12px;
    margin-bottom: 16px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding: 0;
  "
>
  <strong>From:</strong> "Azure DevOps" &lt;<EMAIL>&gt;<br />
  <strong>Sent:</strong> 2/7/2025 4:48:32 μμ<br />
  <strong>To:</strong> <EMAIL>;<EMAIL><br />
  <strong>Subject:</strong> Bug 24339 - bInCharge (SIT) - My Approvals tasks
  list - Document counter counts the also the End WF tasks
</p>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><meta
  http-equiv="X-UA-Compatible"
  content="IE=edge"
/><meta name="viewport" content="width=device-width, initial-scale=1" /><meta
  name="ProgId"
  content="Word.Document"
/>
<title></title>
<div
  style="
    color: white;
    display: none;
    font-size: 1px;
    max-height: 0px;
    max-width: 0px;
    opacity: 0;
    overflow: hidden;
  "
>
  Nektarios Banousi mentioned you in Bug 24339
</div>

<div
  class="container"
  style="
    background: #f8f8f8;
    color: #212121;
    font-family: 'Segoe UI', '-apple-system', 'BlinkMacSystemFont', 'Roboto',
      'Arial', sans-serif;
    font-size: 14px;
  "
>
  <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
    <tbody>
      <tr>
        <td
          class="content"
          style="
            background-color: white;
            border-bottom: solid #eaeaea 1px;
            max-width: 640px;
            padding: 0px;
          "
          width="640"
        >
          <!--[if (gte mso 9)|(IE)]>
            <table
              border="0"
              cellpadding="0"
              cellspacing="0"
              style="width: 640px; height: 0"
            >
              <tr>
                <td></td>
              </tr>
            </table>
          <![endif]-->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tbody>
              <tr>
                <td
                  style="
                    background-color: white;
                    background: white;
                    padding: 20px 24px 25px 24px;
                  "
                >
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    class="responsive-table"
                    style="width: 100%"
                  >
                    <tbody>
                      <tr>
                        <td>
                          <table
                            border="0"
                            cellpadding="0"
                            cellspacing="0"
                            class="logo"
                            style="width: 100%"
                          >
                            <tbody>
                              <tr>
                                <td style="vertical-align: middle">
                                  <img
                                    alt="Microsoft"
                                    height="16"
                                    src="https://cdn.vsassets.io/content/notifications/v3/microsoft.png"
                                    width="80"
                                  />
                                </td>
                                <td
                                  style="
                                    text-align: right;
                                    vertical-align: middle;
                                    word-wrap: none;
                                  "
                                >
                                  <span style="color: #666666">Azure</span>
                                  <strong
                                    style="color: #0078d4; font-weight: bold"
                                    >DevOps</strong
                                  >
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
              <tr>
                <td class="hero" style="padding: 0px 24px">
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    class="responsive-table"
                    style="max-width: 592px; width: 100%"
                  >
                    <tbody>
                      <tr>
                        <td>
                          <table
                            border="0"
                            cellpadding="0"
                            cellspacing="0"
                            style="width: 100%"
                          >
                            <tbody>
                              <tr>
                                <td>
                                  <table>
                                    <tbody>
                                      <tr>
                                        <td
                                          class="title"
                                          style="
                                            color: #212121;
                                            font-size: 28px;
                                            font-weight: bold;
                                            letter-spacing: -0.04em;
                                            line-height: 40px;
                                            padding: 0 0 24px 0;
                                            word-break: break-word;
                                          "
                                        >
                                          <a
                                            href="mailto:<EMAIL>"
                                            >Nektarios Banousi</a
                                          >
                                          mentioned you in
                                          <a
                                            href="https://benefitsoftware.visualstudio.com:443/1eee57b6-12e2-431d-96fa-a24d6bda2d5d/_workitems/edit/24339?src=WorkItemMention&amp;src-action=artifact_link&amp;tracking_data=eyJTb3VyY2UiOiJFbWFpbCIsIlR5cGUiOiJOb3RpZmljYXRpb24iLCJTSUQiOiJtcy52c3MtbWVudGlvbnMuaWRlbnRpdHktbWVudGlvbi1zdWJzY3JpcHRpb24iLCJTVHlwZSI6IkNPTiIsIlJlY2lwIjoyfQ%3d%3d"
                                            >Bug 24339</a
                                          >
                                        </td>
                                      </tr>
                                      <tr>
                                        <td
                                          class="message"
                                          style="padding: 0 0 24px 0"
                                        >
                                          <pre
                                            style="
                                              color: #444444;
                                              display: inline;
                                              font-family: 'Segoe UI',
                                                '-apple-system',
                                                'BlinkMacSystemFont', 'Roboto',
                                                'Arial', sans-serif;
                                              font-size: 14px;
                                              line-height: 20px;
                                              margin-bottom: 0px;
                                              margin-left: 0px;
                                              margin-right: 0px;
                                              margin-top: 0px;
                                              padding: 0px;
                                              white-space: pre-wrap;
                                              word-break: break-word;
                                            "
                                          >
bInCharge (SIT) - My Approvals tasks list - Document counter counts the also the End WF tasks</pre
                                          >
                                        </td>
                                      </tr>
                                      <tr>
                                        <td style="padding: 0 0 24px 0">
                                          <table
                                            border="0"
                                            cellpadding="0"
                                            cellspacing="0"
                                            style="
                                              margin-left: -20px;
                                              position: relative;
                                            "
                                          >
                                            <tbody>
                                              <tr>
                                                <td
                                                  class="comment"
                                                  style="
                                                    background-color: #f8f8f8;
                                                    background: #f8f8f8;
                                                    font-family: 'Segoe UI',
                                                      '-apple-system',
                                                      'BlinkMacSystemFont',
                                                      'Roboto', 'Arial',
                                                      sans-serif;
                                                    font-size: 14px;
                                                    line-height: 20px;
                                                    padding: 12px;
                                                  "
                                                >
                                                  <pre
                                                    class="comment"
                                                    style="
                                                      background: #f8f8f8;
                                                      font-family: 'Segoe UI',
                                                        '-apple-system',
                                                        'BlinkMacSystemFont',
                                                        'Roboto', 'Arial',
                                                        sans-serif;
                                                      font-size: 14px;
                                                      line-height: 20px;
                                                      white-space: pre-wrap;
                                                    "
                                                  >

&nbsp;</pre
                                                  >

                                                  <div>
                                                    <a
                                                      data-vss-mention="version:2.0,878d6b3a-128f-4eca-9f0d-10fa1cf0c687"
                                                      href="#"
                                                      >@Konstantinos
                                                      Thelouras</a
                                                    >&nbsp;Must be fixed from
                                                    the backend<br />
                                                    <br />
                                                    <a
                                                      data-vss-mention="version:2.0,39773678-f592-698a-96fd-8862522977d3"
                                                      href="#"
                                                      >@Dimitris Pappas</a
                                                    >&nbsp;the call
                                                    is&nbsp;<span
                                                      style="
                                                        background-color: #202020;
                                                        color: #cda869;
                                                        display: inline !important;
                                                        font-family: 'Fira Code',
                                                          'SF Mono', Consolas,
                                                          'Segoe UI', Roboto,
                                                          -apple-system,
                                                          'Helvetica Neue',
                                                          sans-serif;
                                                        font-size: 14.1376px;
                                                      "
                                                      >/approvals-module/categories?hasUserPendingTasks=true&amp;groupVessel=false&amp;VSL_GRP_ID=&amp;VSL_ID=&amp;UPDOC_TYPE_ID=&amp;SENDER_ID=&amp;BGACC_ID=</span
                                                    >&nbsp;<br />
                                                    <br />
                                                  </div>

                                                  <div>
                                                    The result is&nbsp;<br />
                                                    <br />
                                                    <span>[</span><br />
                                                    <div>&nbsp; {<br /></div>

                                                    <div>
                                                      &nbsp; &nbsp; "ID": 1,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "VSL_ID":
                                                      null,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "VSL_NAME":
                                                      null,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp;
                                                      "Category_ID": 40,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "Category":
                                                      "Evaluation Report",<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "Number":
                                                      15,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp;
                                                      "Importance": 0<br />
                                                    </div>

                                                    <div>&nbsp; },<br /></div>

                                                    <div>&nbsp; {<br /></div>

                                                    <div>
                                                      &nbsp; &nbsp; "ID": 3,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "VSL_ID":
                                                      null,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "VSL_NAME":
                                                      null,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp;
                                                      "Category_ID": 70,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "Category":
                                                      "Invoice",<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "Number":
                                                      40,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp;
                                                      "Importance": 0<br />
                                                    </div>

                                                    <div>&nbsp; },<br /></div>

                                                    <div>&nbsp; {<br /></div>

                                                    <div>
                                                      &nbsp; &nbsp; "ID": 2,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "VSL_ID":
                                                      null,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "VSL_NAME":
                                                      null,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp;
                                                      "Category_ID": 50,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "Category":
                                                      "Order",<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp; "Number":
                                                      17,<br />
                                                    </div>

                                                    <div>
                                                      &nbsp; &nbsp;
                                                      "Importance": 0<br />
                                                    </div>

                                                    <div>&nbsp; }<br /></div>
                                                    <span>]</span><br />
                                                  </div>
                                                </td>
                                              </tr>
                                            </tbody>
                                          </table>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td style="padding: 0 0 24px 0">
                                          <table
                                            border="0"
                                            cellpadding="0"
                                            cellspacing="0"
                                            style="width: 100%"
                                          >
                                            <tbody>
                                              <tr>
                                                <td>
                                                  <table
                                                    border="0"
                                                    cellpadding="0"
                                                    cellspacing="0"
                                                  >
                                                    <tbody>
                                                      <tr>
                                                        <td
                                                          align="center"
                                                          bgcolor="#007acc"
                                                          style="
                                                            border-radius: 2px;
                                                            padding: 8px 12px;
                                                          "
                                                        >
                                                          <a
                                                            class="mobile-button btn-primary"
                                                            href="https://benefitsoftware.visualstudio.com:443/1eee57b6-12e2-431d-96fa-a24d6bda2d5d/_workitems/edit/24339?src=WorkItemMention&amp;src-action=artifact_link&amp;tracking_data=eyJTb3VyY2UiOiJFbWFpbCIsIlR5cGUiOiJOb3RpZmljYXRpb24iLCJTSUQiOiJtcy52c3MtbWVudGlvbnMuaWRlbnRpdHktbWVudGlvbi1zdWJzY3JpcHRpb24iLCJTVHlwZSI6IkNPTiIsIlJlY2lwIjoyLCJfeGNpIjp7Ik5JRCI6NDEyODU1NzQ0LCJNUmVjaXAiOiJtMD0yICIsIkFjdCI6IjEzMDIxMTYzLTBiZTEtNGU2MS05MDQ3LTQxYWZmZjRhMjc2ZCJ9LCJFbGVtZW50IjoiaGVyby9jdGEifQ%3d%3d"
                                                            style="
                                                              font-weight: 500;
                                                              font-size: 14px;
                                                              text-decoration: none;
                                                              padding: 0px;
                                                              display: inline-block;
                                                              color: #ffffff;
                                                            "
                                                            >View work item
                                                          </a>
                                                        </td>
                                                      </tr>
                                                    </tbody>
                                                  </table>
                                                </td>
                                              </tr>
                                            </tbody>
                                          </table>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
      <tr>
        <td
          class="content"
          style="
            background-color: #f8f8f8;
            background: #f8f8f8;
            max-width: 640px;
            padding: 0px;
          "
          width="640"
        >
          <!--[if (gte mso 9)|(IE)]>
            <table
              border="0"
              cellpadding="0"
              cellspacing="0"
              style="width: 640px; height: 0"
            >
              <tr>
                <td></td>
              </tr>
            </table>
          <![endif]-->
          <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
            <tbody>
              <tr>
                <td style="padding: 24px 24px 24px 24px">
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    class="responsive-table"
                    style="width: 100%"
                  >
                    <tbody>
                      <tr>
                        <td
                          style="
                            color: #666666;
                            font-size: 12px;
                            line-height: 18px;
                          "
                        >
                          We sent you this notification because you were
                          mentioned.
                        </td>
                      </tr>
                      <tr>
                        <td
                          style="
                            color: #666666;
                            font-size: 12px;
                            line-height: 18px;
                            padding-top: 4px;
                          "
                        >
                          Microsoft respects your privacy. Review our Online
                          Services
                          <a
                            href="https://privacy.microsoft.com/privacystatement"
                            >Privacy Statement</a
                          >.<br />
                          One Microsoft Way, Redmond, WA, USA 98052.
                        </td>
                      </tr>
                      <tr>
                        <td
                          style="
                            color: #212121;
                            font-size: 12px;
                            line-height: 18px;
                            padding-top: 4px;
                          "
                        >
                          Sent from Azure
                          <strong style="color: #0078d4; font-weight: bold"
                            >DevOps</strong
                          >
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<p
  style="
    font-family: Verdana, Arial, sans-serif;
    font-size: 12px;
    margin-bottom: 16px;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding: 0;
  "
>
  <img
    alt=""
    aria-hidden="true"
    height="1"
    src="https://mucp.api.account.microsoft.com/m/v2/v?d=AIAADQVUOUJSLOLIGPD4VVZBMEFYTH42GEGTODRMJEGXDAVNRPC25HXG2Y7B3ZHYW5SB7M6N7WNBMPU7KOVNX5PLLC7M54C42O6NAVDP55OLQPOMLL5G2IZXPCS5ASQ5Q53EUY3NFJSXE7DIUXIL46Y3WZ3GLYI&amp;i=AIAADCD2XZPD7II3WQ7ZLMYEDCRYZ3NA4CO6HLIDOUXYV6NR4VQNI7DYJDMQYKSEYVSFELJJEELNR7TNQH7EI4Y6HV3HOB4YVRCQZEGBBYTMMMK6MHXYTCD34E4BHYUTZGJS5WOJXQIRFGLMZKVKFJCQKESO34X4MTSGF2NIZI4YNXLGT3FO3FFCZRXSPPMYM2XZHDEUMIMYR72I6BFTQHCSN3IVSKI24PI7M6LRBF6NH2Z6H4J64AHKTRFJXH6VVOW75WDY7WDOE4VM7PWDD2VKNFVPVII"
    tabindex="-1"
    width="1"
  />
</p>
`,
      }}
      enableExperimentalMarginCollapsing={true}
      computeEmbeddedMaxWidth={(contentWidth) => contentWidth}
      ignoredDomTags={["map", "area"]}
      renderersProps={{
        img: {
          enableExperimentalPercentWidth: true,
        },
      }}
      defaultTextProps={{
        style: styles.htmlTextStyle,
      }}
      tagsStyles={{
        body: {
          fontSize: FONT_SIZES.FOURTEEN,
          lineHeight: 20,
        },
        p: {
          marginBottom: 12,
        },
        div: {
          marginBottom: 8,
        },
      }}
      systemFonts={["System"]}
    />
  );
};

// HtmlMessageContent.displayName = 'HtmlMessageContent';

const createStyles = () => {
  const styles = StyleSheet.create({
    scrollContainer: {
      flex: 1,
    },
    htmlTextStyle: {
      color: "#000000",
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: 20,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: SPACING.L,
    },
    retryButton: {
      marginTop: SPACING.M,
      paddingHorizontal: SPACING.L,
      paddingVertical: SPACING.S,
      backgroundColor: "#FFFFFF",
      borderRadius: 8,
      borderWidth: 1,
      borderColor: "#007AFF",
    },
    retryButtonText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "600",
      color: "#007AFF",
    },
    // Android-specific styles
    androidScrollContent: {
      flexGrow: 1,
    },
    androidHorizontalScroll: {
      flex: 1,
    },
    androidOuterScroll: {
      flex: 1,
      height: "100%",
    },
    androidHorizontalContent: {
      minWidth: "100%",
      flexGrow: 1,
    },
    androidInnerScroll: {
      width: 800, // Fixed width for the inner scroll
      minHeight: "100%",
    },
    androidWideContentContainer: {
      flex: 1,
      minHeight: 400, // Ensure minimum height
    },
    androidHorizontalScrollOnly: {
      flexGrow: 0,
      height: "auto",
    },
  });

  return styles;
};

export default React.memo(HtmlMessageContent);
